<!-- 顶部标题 -->
<template>
  <div class="header-container">
    <header class="header"></header>
    
    <!-- 能耗信息面板 -->
    <div class="energy-dashboard">
      <!-- 背景图片 -->
      <div class="background-image"></div>
      
      <!-- 能耗信息面板 -->
      <div class="energy-panels">
        <!-- 园区总能耗 -->
        <div class="energy-panel">
          <div class="panel-icon">
            <img src="@/assets/img/sd_icon.png" alt="能耗图标" />
          </div>
          <div class="panel-content">
            <div class="panel-label">园区总能耗</div>
            <div class="panel-value">{{ totalEnergy }}</div>
            <div class="panel-unit">kwh</div>
          </div>
        </div>
        
        <!-- 单位面积能耗 -->
        <div class="energy-panel">
          <div class="panel-icon">
            <img src="@/assets/img/sd_icon.png" alt="能耗图标" />
          </div>
          <div class="panel-content">
            <div class="panel-label">单位面积能耗</div>
            <div class="panel-value">{{ unitEnergy }}</div>
            <div class="panel-unit">kwh/m³</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 能耗数据 - 后期通过接口获取
const totalEnergy = ref(1000)
const unitEnergy = ref(100)

// 获取能耗数据的函数
const fetchEnergyData = async () => {
  try {
    // TODO: 这里替换为实际的API调用
    // const response = await fetch('/api/energy-data')
    // const data = await response.json()
    // totalEnergy.value = data.totalEnergy
    // unitEnergy.value = data.unitEnergy
    
    // 模拟数据更新
    console.log('获取能耗数据...')
  } catch (error) {
    console.error('获取能耗数据失败:', error)
  }
}

// 组件挂载后获取数据
onMounted(() => {
  fetchEnergyData()
  
  // 可以设置定时刷新，比如每5分钟更新一次
  // setInterval(fetchEnergyData, 5 * 60 * 1000)
})
</script>

<style lang="scss" scoped>
.header-container {
  position: relative;
  width: 100%;
}

.header {
  position: absolute;
  margin: 0 12px;
  top: 0;
  width: calc(100% - 24px);
  height: 111px;
  background: url('@/assets/img/headBg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
  z-index: 10;
}

.energy-dashboard {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/img/sd_bottom.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
}

.energy-panels {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  gap: 40px; /* 增加水平间距 */
  padding: 120px 50px 50px; /* 顶部留出头部空间 */
  align-items: center;
  justify-content: center; /* 水平居中 */
}

.energy-panel {
  display: flex;
  align-items: center;
  background: rgba(232, 252, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px 30px;
  box-shadow: 0 0 20px rgba(161, 241, 255, 0.3);
  border: 1px solid rgba(161, 241, 255, 0.2);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(161, 241, 255, 0.1) 0%, rgba(232, 252, 255, 0.05) 100%);
    z-index: -1;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(161, 241, 255, 0.3), rgba(232, 252, 255, 0.1));
    border-radius: 22px;
    z-index: -2;
    opacity: 0.5;
  }
}

.panel-icon {
  width: 60px;
  height: 60px;
  margin-right: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 10px rgba(161, 241, 255, 0.5));
  }
}

.panel-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.panel-label {
  font-size: 18px;
  color: #E8FCFF;
  font-weight: 500;
  white-space: nowrap;
}

.panel-value {
  font-size: 32px;
  color: #A1F1FF;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(161, 241, 255, 0.5);
}

.panel-unit {
  font-size: 16px;
  color: #E8FCFF;
  font-weight: 400;
  white-space: nowrap;
}

// 响应式设计
@media (max-width: 768px) {
  .energy-panels {
    padding: 120px 30px 50px;
    gap: 20px;
    flex-direction: column; /* 移动端改为垂直排列 */
  }
  
  .energy-panel {
    padding: 15px 20px;
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .panel-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .panel-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .panel-value {
    font-size: 28px;
  }
}
</style>
