<!-- 顶部标题 -->
<template>
  <div class="header-container">
    <header class="header"></header>
    
    <!-- 能耗信息面板 -->
    <div class="energy-dashboard">
      <!-- 背景图片 -->
      <div class="background-image"></div>
      
      <!-- 能耗信息面板 - 两个面板挨在一起 -->
      <div class="energy-panels">
        <!-- 园区总能耗 -->
        <div class="energy-panel">
          <div class="panel-icon">
            <img src="@/assets/img/sd_icon.png" alt="能耗图标" />
          </div>
          <div class="panel-content">
            <div class="panel-label">园区总能耗</div>
            <div class="panel-value">{{ totalEnergy }}</div>
            <div class="panel-unit">kwh</div>
          </div>
        </div>
        
        <!-- 单位面积能耗 -->
        <div class="energy-panel">
          <div class="panel-icon">
            <img src="@/assets/img/sd_icon.png" alt="能耗图标" />
          </div>
          <div class="panel-content">
            <div class="panel-label">单位面积能耗</div>
            <div class="panel-value">{{ unitEnergy }}</div>
            <div class="panel-unit">kwh/m³</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 能耗数据 - 后期通过接口获取
const totalEnergy = ref(1000)
const unitEnergy = ref(100)

// 获取能耗数据的函数
const fetchEnergyData = async () => {
  try {
    // TODO: 这里替换为实际的API调用
    // const response = await fetch('/api/energy-data')
    // const data = await response.json()
    // totalEnergy.value = data.totalEnergy
    // unitEnergy.value = data.unitEnergy
    
    // 模拟数据更新
    console.log('获取能耗数据...')
  } catch (error) {
    console.error('获取能耗数据失败:', error)
  }
}

// 组件挂载后获取数据
onMounted(() => {
  fetchEnergyData()
  
  // 可以设置定时刷新，比如每5分钟更新一次
  // setInterval(fetchEnergyData, 5 * 60 * 1000)
})
</script>

<style lang="scss" scoped>
.header-container {
  position: relative;
  width: 100%;
}

.header {
  position: absolute;
  margin: 0 12px;
  top: 0;
  width: calc(100% - 24px);
  height: 111px;
  background: url('@/assets/img/headBg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
  z-index: 10;
}

.energy-dashboard {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/img/sd_bottom.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
}

.energy-panels {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-top: 120px; /* 顶部留出头部空间 */
  /* 移除gap，让两个面板挨在一起 */
}

.energy-panel {
  display: flex;
  align-items: center;
  /* 移除独立的背景和边框，让文字直接显示在sd_bottom.png上 */
  padding: 15px 30px;
  position: relative;
  
  /* 添加连接线效果 */
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 60%;
    background: linear-gradient(to bottom, transparent, rgba(161, 241, 255, 0.5), transparent);
  }
}

.panel-icon {
  width: 50px;
  height: 50px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 0 8px rgba(161, 241, 255, 0.6));
  }
}

.panel-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-label {
  font-size: 16px;
  color: #E8FCFF;
  font-weight: 500;
  white-space: nowrap;
}

.panel-value {
  font-size: 28px;
  color: #A1F1FF;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(161, 241, 255, 0.5);
}

.panel-unit {
  font-size: 14px;
  color: #E8FCFF;
  font-weight: 400;
  white-space: nowrap;
}

// 响应式设计
@media (max-width: 768px) {
  .energy-panels {
    padding-top: 120px;
    flex-direction: column; /* 移动端改为垂直排列 */
    gap: 20px;
  }
  
  .energy-panel {
    padding: 15px 20px;
    flex-direction: column;
    text-align: center;
    gap: 10px;
    
    &:not(:last-child)::after {
      display: none; /* 移动端隐藏连接线 */
    }
  }
  
  .panel-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .panel-content {
    flex-direction: column;
    gap: 6px;
  }
  
  .panel-value {
    font-size: 24px;
  }
}
</style>
