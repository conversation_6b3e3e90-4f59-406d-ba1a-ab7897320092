<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <img src="@/assets/img/nh_title.png" alt="标题" class="title-image" />
      </div>
    </template>
    <template #content>
      <div class="inspection-container" :style="containerStyle">
        <!-- 两个区域并排显示 -->
        <div class="sections-row">
          <!-- 桥梁区域 -->
          <div class="section">
            <!-- 桥梁标题 -->
            <div class="section-title">桥梁</div>

            <!-- 桥梁内容 -->
            <div class="chart-section">
              <div class="chart-container">
                <CEcharts :option="bridgeOption" class="progress-chart" />
                <div class="chart-center">
                  <div class="percentage">0%</div>
                  <div class="label">已巡检</div>
                </div>
              </div>
              <div class="stats-info">
                <div class="stat-item">
                  <div class="stat-line green"></div>
                  <div class="stat-content">
                    <span class="stat-label">已巡检</span>
                    <span class="stat-value">0个</span>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-line gray"></div>
                  <div class="stat-content">
                    <span class="stat-label">未巡检</span>
                    <span class="stat-value">58个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 隧道区域 -->
          <div class="section">
            <!-- 隧道标题 -->
            <div class="section-title">隧道</div>

            <!-- 隧道内容 -->
            <div class="chart-section">
              <div class="chart-container">
                <CEcharts :option="tunnelOption" class="progress-chart" />
                <div class="chart-center">
                  <div class="percentage">0%</div>
                  <div class="label">已巡检</div>
                </div>
              </div>
              <div class="stats-info">
                <div class="stat-item">
                  <div class="stat-line green"></div>
                  <div class="stat-content">
                    <span class="stat-label">已巡检</span>
                    <span class="stat-value">0个</span>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-line gray"></div>
                  <div class="stat-content">
                    <span class="stat-label">未巡检</span>
                    <span class="stat-value">9个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import nhBg from '@/assets/img/nh_bg.png'

const bridgeOption = ref({})
const tunnelOption = ref({})

// 背景图样式
const containerStyle = computed(() => ({
  backgroundImage: `url(${nhBg})`,
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center'
}))

// 创建圆形进度图表配置
const createProgressChart = (percentage = 0, color = '#00D4FF') => {
  return {
    series: [
      {
        type: 'pie',
        radius: ['70%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: percentage,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: color },
                { offset: 1, color: color + '80' }
              ])
            }
          },
          {
            value: 100 - percentage,
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        silent: true
      }
    ]
  }
}

onMounted(() => {
  // 桥梁进度图表 - 蓝色
  bridgeOption.value = createProgressChart(0, '#00D4FF')
  // 隧道进度图表 - 蓝色
  tunnelOption.value = createProgressChart(0, '#00D4FF')
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;

  .title-image {
    height: 40px;
    width: auto;
  }
}

.inspection-container {
  padding: 20px 0;
  min-height: 300px;
}

.sections-row {
  display: flex;
  gap: 40px;
  justify-content: space-between;
}

.section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  padding: 8px 24px;
  background-image: url('@/assets/images/bg_panel1_top_box.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  font-size: 14px;
  margin-bottom: 20px;
  position: relative;
  min-width: 80px;
  text-align: center;
}

.chart-section {
  display: flex;
  align-items: center;
  // gap: 30px;
}

.chart-container {
  position: relative;
  width: 120px;
  height: 120px;

  .progress-chart {
    width: 100%;
    height: 100%;
  }

  .chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .percentage {
      font-size: 20px;
      font-weight: bold;
      color: #00D4FF;
      line-height: 1;
    }

    .label {
      font-size: 12px;
      color: #C5D6E6;
      margin-top: 4px;
    }
  }
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 120px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .stat-line {
      width: 4px;
      height: 40px;
      border-radius: 2px;

      &.green {
        background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
      }

      &.gray {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .stat-label {
        font-size: 14px;
        color: #C5D6E6;
      }

      .stat-value {
        font-size: 16px;
        color: #00D4FF;
        font-weight: bold;
      }
    }
  }
}
</style>
