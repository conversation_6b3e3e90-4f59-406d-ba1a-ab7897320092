<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <img src="@/assets/img/nh_title.png" alt="标题" class="title-image" />
      </div>
    </template>
    <template #content>
      <div class="inspection-container" :style="containerStyle">
        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 这里可以添加新的内容 -->
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import nhBg from '@/assets/img/nh_bg.png'

// 背景图样式
const containerStyle = computed(() => ({
  backgroundImage: `url(${nhBg})`,
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center'
}))

onMounted(() => {
  // 初始化代码
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;

  .title-image {
    height: 40px;
    width: auto;
  }
}

.inspection-container {
  padding: 20px 0;
  min-height: 300px;
}

.content-area {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}
</style>
